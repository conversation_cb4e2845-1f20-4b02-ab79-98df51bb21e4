{"name": "@toast-ui/editor", "version": "3.2.2", "description": "GFM  Markdown Wysiwyg Editor - Productive and Extensible", "keywords": ["nhn", "nhn cloud", "toast", "<PERSON><PERSON>", "toast-ui", "markdown", "wysiwyg", "editor", "preview", "gfm"], "main": "dist/toastui-editor.js", "module": "dist/esm/", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/toastui-editor.js"}, "./viewer": {"import": "./dist/esm/indexViewer.js", "require": "./dist/toastui-editor-viewer.js"}, "./dist/i18n/*": {"import": "./dist/esm/i18n/*.js", "require": "./dist/i18n/*.js"}, "./dist/toastui-editor-viewer": "./dist/toastui-editor-viewer.js", "./dist/toastui-editor.css": "./dist/toastui-editor.css", "./dist/toastui-editor-viewer.css": "./dist/toastui-editor-viewer.css", "./dist/toastui-editor-only.css": "./dist/toastui-editor-only.css", "./dist/theme/toastui-editor-dark.css": "./dist/theme/toastui-editor-dark.css", "./toastui-editor.css": "./dist/toastui-editor.css", "./toastui-editor-viewer.css": "./dist/toastui-editor-viewer.css", "./toastui-editor-only.css": "./dist/toastui-editor-only.css", "./toastui-editor-dark.css": "./dist/theme/toastui-editor-dark.css"}, "types": "types/index.d.ts", "files": ["dist/*.js", "dist/*.css", "dist/theme", "dist/esm", "dist/i18n", "types"], "author": "NHN Cloud FE Development Lab <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/nhn/tui.editor.git", "directory": "apps/editor"}, "bugs": {"url": "https://github.com/nhn/tui.editor/issues"}, "homepage": "https://ui.toast.com", "browserslist": "last 2 versions, not ie <= 10", "scripts": {"lint": "eslint .", "test:types": "tsc", "test": "jest --watch", "test:ci": "jest", "serve": "snowpack dev", "serve:ie": "webpack serve", "build:i18n": "cross-env webpack --config scripts/webpack.config.i18n.js && webpack --config scripts/webpack.config.i18n.js --env minify", "build:prod": "cross-env webpack build && webpack build --env minify && node tsBannerGenerator.js", "build": "npm run build:esm && npm run build:i18n && npm run build:prod", "build:esm": "rollup -c", "note": "tui-note --tag=$(git describe --tags)", "ts2js": "tsc --outDir tmpdoc --sourceMap false --target ES2015 --noEmit false", "doc:dev": "npm run ts2js && tuidoc --serv", "doc": "npm run ts2js && tuidoc"}, "devDependencies": {"@toast-ui/release-notes": "^2.0.1", "@types/dompurify": "2.3.3", "cross-env": "^6.0.3"}, "dependencies": {"dompurify": "^2.3.3", "prosemirror-commands": "^1.1.9", "prosemirror-history": "^1.1.3", "prosemirror-inputrules": "^1.1.3", "prosemirror-keymap": "^1.1.4", "prosemirror-model": "^1.14.1", "prosemirror-state": "^1.3.4", "prosemirror-view": "^1.18.7"}}
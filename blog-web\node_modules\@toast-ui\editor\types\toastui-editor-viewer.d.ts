declare module '@toast-ui/editor/dist/toastui-editor-viewer' {
  import {
    Viewer,
    ViewerOptions,
    ExtendedAutolinks,
    LinkAttributes,
    Sanitizer,
    EventMap,
    WidgetRuleMap,
    WidgetRule,
    PluginContext,
    I18n,
    CustomHTMLRenderer,
    HTMLMdNodeConvertor,
    HTMLMdNodeConvertorMap,
    PluginInfo,
    PluginNodeViews,
    PluginCommandMap,
  } from '@toast-ui/editor';

  export {
    ViewerOptions,
    ExtendedAutolinks,
    LinkAttributes,
    Sanitizer,
    EventMap,
    WidgetRuleMap,
    WidgetRule,
    PluginContext,
    I18n,
    CustomHTMLRenderer,
    HTMLMdNodeConvertor,
    HTMLMdNodeConvertorMap,
    PluginInfo,
    PluginNodeViews,
    PluginCommandMap,
  };
  export default Viewer;
}

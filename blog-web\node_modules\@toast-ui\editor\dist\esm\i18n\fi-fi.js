/**
 * @toast-ui/editor : i18n
 * @version 3.2.2 | Fri Feb 17 2023
 * <AUTHOR> Cloud FE Development Lab <<EMAIL>>
 * @license MIT
 */

import Editor from '@toast-ui/editor';

/**
 * @fileoverview I18N for Finnish
 * <AUTHOR> <<EMAIL>>
 */
Editor.setLanguage(['fi', 'fi-FI'], {
    Markdown: 'Markdown',
    WYSIWYG: 'WYSIWYG',
    Write: 'Kir<PERSON><PERSON>',
    Preview: 'Esikatselu',
    Headings: 'Otsikot',
    Paragraph: 'Kappale',
    Bold: 'Lihavointi',
    Italic: 'Kursivointi',
    Strike: 'Yliviivaus',
    Code: 'Koodi',
    Line: 'Vaakaviiva',
    Blockquote: 'Lainaus',
    'Unordered list': 'Luettelo',
    'Ordered list': 'Numeroitu luettelo',
    Task: 'Tehtävä',
    Indent: '<PERSON><PERSON>na sisennyst<PERSON>',
    Outdent: 'Pienennä sisennystä',
    'Insert link': '<PERSON><PERSON><PERSON><PERSON> linkki',
    'Insert CodeBlock': '<PERSON>s<PERSON><PERSON> koodia',
    'Insert table': '<PERSON><PERSON><PERSON><PERSON> taulukko',
    'Insert image': 'Lisää kuva',
    Heading: 'Otsikko',
    'Image URL': 'Kuvan URL',
    'Select image file': 'Valitse kuvatiedosto',
    'Choose a file': 'Valitse tiedosto',
    'No file': 'Ei tiedosto',
    Description: 'Kuvaus',
    OK: 'OK',
    More: 'Lisää',
    Cancel: 'Peruuta',
    File: 'Tiedosto',
    URL: 'URL',
    'Link text': 'Linkkiteksti',
    'Add row to up': 'Lisää rivi ylöspäin',
    'Add row to down': 'Lisää rivi alaspäin',
    'Add column to left': 'Lisää sarake vasemmalla',
    'Add column to right': 'Lisää sarake oikealle',
    'Remove row': 'Poista rivi',
    'Remove column': 'Poista sarake',
    'Align column to left': 'Tasaus vasemmalle',
    'Align column to center': 'Keskitä',
    'Align column to right': 'Tasaus oikealle',
    'Remove table': 'Poista taulukko',
    'Would you like to paste as table?': 'Haluatko liittää taulukkomuodossa?',
    'Text color': 'Tekstin väri',
    'Auto scroll enabled': 'Automaattinen skrollaus käytössä',
    'Auto scroll disabled': 'Automaattinen skrollaus pois käytöstä',
    'Choose language': 'Valitse kieli',
});

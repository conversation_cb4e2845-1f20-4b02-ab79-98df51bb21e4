/**
 * @toast-ui/editor : i18n
 * @version 3.2.2 | Fri Feb 17 2023
 * <AUTHOR> Cloud FE Development Lab <<EMAIL>>
 * @license MIT
 */

import Editor from '@toast-ui/editor';

/**
 * @fileoverview I18N for English
 * <AUTHOR> Cloud FE Development Lab <<EMAIL>>
 */
Editor.setLanguage(['en', 'en-US'], {
    Markdown: 'Markdown',
    WYSIWYG: 'WYSIWYG',
    Write: 'Write',
    Preview: 'Preview',
    Headings: 'Headings',
    Paragraph: 'Paragraph',
    Bold: 'Bold',
    Italic: 'Italic',
    Strike: 'Strike',
    Code: 'Inline code',
    Line: 'Line',
    Blockquote: 'Blockquote',
    'Unordered list': 'Unordered list',
    'Ordered list': 'Ordered list',
    Task: 'Task',
    Indent: 'Indent',
    Outdent: 'Outdent',
    'Insert link': 'Insert link',
    'Insert CodeBlock': 'Insert codeBlock',
    'Insert table': 'Insert table',
    'Insert image': 'Insert image',
    Heading: 'Heading',
    'Image URL': 'Image URL',
    'Select image file': 'Select image file',
    'Choose a file': 'Choose a file',
    'No file': 'No file',
    Description: 'Description',
    OK: 'OK',
    More: 'More',
    Cancel: 'Cancel',
    File: 'File',
    URL: 'URL',
    'Link text': 'Link text',
    'Add row to up': 'Add row to up',
    'Add row to down': 'Add row to down',
    'Add column to left': 'Add column to left',
    'Add column to right': 'Add column to right',
    'Remove row': 'Remove row',
    'Remove column': 'Remove column',
    'Align column to left': 'Align column to left',
    'Align column to center': 'Align column to center',
    'Align column to right': 'Align column to right',
    'Remove table': 'Remove table',
    'Would you like to paste as table?': 'Would you like to paste as table?',
    'Text color': 'Text color',
    'Auto scroll enabled': 'Auto scroll enabled',
    'Auto scroll disabled': 'Auto scroll disabled',
    'Choose language': 'Choose language',
});

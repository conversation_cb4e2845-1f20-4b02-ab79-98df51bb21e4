ProseMirror's view module displays a given [editor
state](#state.EditorState) in the DOM, and handles user events.

Make sure you load `style/prosemirror.css` as a stylesheet when using
this module.

@EditorView

### Props

@EditorProps

@NodeViewConstructor

@MarkViewConstructor

@DirectEditorProps

@NodeView

@MarkView

@ViewMutationRecord

@DOMEventMap

### Decorations

Decorations make it possible to influence the way the document is
drawn, without actually changing the document.

@Decoration

@DecorationAttrs

@DecorationSet

@DecorationSource

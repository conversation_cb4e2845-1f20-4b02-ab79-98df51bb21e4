{"name": "prosemirror-model", "version": "1.25.1", "description": "ProseMirror's document model", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "sideEffects": false, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://marijnhaverbeke.nl"}], "repository": {"type": "git", "url": "git://github.com/prosemirror/prosemirror-model.git"}, "dependencies": {"orderedmap": "^2.0.0"}, "devDependencies": {"@prosemirror/buildhelper": "^0.1.5", "jsdom": "^20.0.0", "prosemirror-test-builder": "^1.0.0"}, "scripts": {"test": "pm-runtests", "prepare": "pm-buildhelper src/index.ts"}}
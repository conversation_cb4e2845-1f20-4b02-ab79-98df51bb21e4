{"version": 3, "sources": ["../../nprogress/nprogress.js"], "sourcesContent": ["/* NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress\n * @license MIT */\n\n;(function(root, factory) {\n\n  if (typeof define === 'function' && define.amd) {\n    define(factory);\n  } else if (typeof exports === 'object') {\n    module.exports = factory();\n  } else {\n    root.NProgress = factory();\n  }\n\n})(this, function() {\n  var NProgress = {};\n\n  NProgress.version = '0.2.0';\n\n  var Settings = NProgress.settings = {\n    minimum: 0.08,\n    easing: 'ease',\n    positionUsing: '',\n    speed: 200,\n    trickle: true,\n    trickleRate: 0.02,\n    trickleSpeed: 800,\n    showSpinner: true,\n    barSelector: '[role=\"bar\"]',\n    spinnerSelector: '[role=\"spinner\"]',\n    parent: 'body',\n    template: '<div class=\"bar\" role=\"bar\"><div class=\"peg\"></div></div><div class=\"spinner\" role=\"spinner\"><div class=\"spinner-icon\"></div></div>'\n  };\n\n  /**\n   * Updates configuration.\n   *\n   *     NProgress.configure({\n   *       minimum: 0.1\n   *     });\n   */\n  NProgress.configure = function(options) {\n    var key, value;\n    for (key in options) {\n      value = options[key];\n      if (value !== undefined && options.hasOwnProperty(key)) Settings[key] = value;\n    }\n\n    return this;\n  };\n\n  /**\n   * Last number.\n   */\n\n  NProgress.status = null;\n\n  /**\n   * Sets the progress bar status, where `n` is a number from `0.0` to `1.0`.\n   *\n   *     NProgress.set(0.4);\n   *     NProgress.set(1.0);\n   */\n\n  NProgress.set = function(n) {\n    var started = NProgress.isStarted();\n\n    n = clamp(n, Settings.minimum, 1);\n    NProgress.status = (n === 1 ? null : n);\n\n    var progress = NProgress.render(!started),\n        bar      = progress.querySelector(Settings.barSelector),\n        speed    = Settings.speed,\n        ease     = Settings.easing;\n\n    progress.offsetWidth; /* Repaint */\n\n    queue(function(next) {\n      // Set positionUsing if it hasn't already been set\n      if (Settings.positionUsing === '') Settings.positionUsing = NProgress.getPositioningCSS();\n\n      // Add transition\n      css(bar, barPositionCSS(n, speed, ease));\n\n      if (n === 1) {\n        // Fade out\n        css(progress, { \n          transition: 'none', \n          opacity: 1 \n        });\n        progress.offsetWidth; /* Repaint */\n\n        setTimeout(function() {\n          css(progress, { \n            transition: 'all ' + speed + 'ms linear', \n            opacity: 0 \n          });\n          setTimeout(function() {\n            NProgress.remove();\n            next();\n          }, speed);\n        }, speed);\n      } else {\n        setTimeout(next, speed);\n      }\n    });\n\n    return this;\n  };\n\n  NProgress.isStarted = function() {\n    return typeof NProgress.status === 'number';\n  };\n\n  /**\n   * Shows the progress bar.\n   * This is the same as setting the status to 0%, except that it doesn't go backwards.\n   *\n   *     NProgress.start();\n   *\n   */\n  NProgress.start = function() {\n    if (!NProgress.status) NProgress.set(0);\n\n    var work = function() {\n      setTimeout(function() {\n        if (!NProgress.status) return;\n        NProgress.trickle();\n        work();\n      }, Settings.trickleSpeed);\n    };\n\n    if (Settings.trickle) work();\n\n    return this;\n  };\n\n  /**\n   * Hides the progress bar.\n   * This is the *sort of* the same as setting the status to 100%, with the\n   * difference being `done()` makes some placebo effect of some realistic motion.\n   *\n   *     NProgress.done();\n   *\n   * If `true` is passed, it will show the progress bar even if its hidden.\n   *\n   *     NProgress.done(true);\n   */\n\n  NProgress.done = function(force) {\n    if (!force && !NProgress.status) return this;\n\n    return NProgress.inc(0.3 + 0.5 * Math.random()).set(1);\n  };\n\n  /**\n   * Increments by a random amount.\n   */\n\n  NProgress.inc = function(amount) {\n    var n = NProgress.status;\n\n    if (!n) {\n      return NProgress.start();\n    } else {\n      if (typeof amount !== 'number') {\n        amount = (1 - n) * clamp(Math.random() * n, 0.1, 0.95);\n      }\n\n      n = clamp(n + amount, 0, 0.994);\n      return NProgress.set(n);\n    }\n  };\n\n  NProgress.trickle = function() {\n    return NProgress.inc(Math.random() * Settings.trickleRate);\n  };\n\n  /**\n   * Waits for all supplied jQuery promises and\n   * increases the progress as the promises resolve.\n   *\n   * @param $promise jQUery Promise\n   */\n  (function() {\n    var initial = 0, current = 0;\n\n    NProgress.promise = function($promise) {\n      if (!$promise || $promise.state() === \"resolved\") {\n        return this;\n      }\n\n      if (current === 0) {\n        NProgress.start();\n      }\n\n      initial++;\n      current++;\n\n      $promise.always(function() {\n        current--;\n        if (current === 0) {\n            initial = 0;\n            NProgress.done();\n        } else {\n            NProgress.set((initial - current) / initial);\n        }\n      });\n\n      return this;\n    };\n\n  })();\n\n  /**\n   * (Internal) renders the progress bar markup based on the `template`\n   * setting.\n   */\n\n  NProgress.render = function(fromStart) {\n    if (NProgress.isRendered()) return document.getElementById('nprogress');\n\n    addClass(document.documentElement, 'nprogress-busy');\n    \n    var progress = document.createElement('div');\n    progress.id = 'nprogress';\n    progress.innerHTML = Settings.template;\n\n    var bar      = progress.querySelector(Settings.barSelector),\n        perc     = fromStart ? '-100' : toBarPerc(NProgress.status || 0),\n        parent   = document.querySelector(Settings.parent),\n        spinner;\n    \n    css(bar, {\n      transition: 'all 0 linear',\n      transform: 'translate3d(' + perc + '%,0,0)'\n    });\n\n    if (!Settings.showSpinner) {\n      spinner = progress.querySelector(Settings.spinnerSelector);\n      spinner && removeElement(spinner);\n    }\n\n    if (parent != document.body) {\n      addClass(parent, 'nprogress-custom-parent');\n    }\n\n    parent.appendChild(progress);\n    return progress;\n  };\n\n  /**\n   * Removes the element. Opposite of render().\n   */\n\n  NProgress.remove = function() {\n    removeClass(document.documentElement, 'nprogress-busy');\n    removeClass(document.querySelector(Settings.parent), 'nprogress-custom-parent');\n    var progress = document.getElementById('nprogress');\n    progress && removeElement(progress);\n  };\n\n  /**\n   * Checks if the progress bar is rendered.\n   */\n\n  NProgress.isRendered = function() {\n    return !!document.getElementById('nprogress');\n  };\n\n  /**\n   * Determine which positioning CSS rule to use.\n   */\n\n  NProgress.getPositioningCSS = function() {\n    // Sniff on document.body.style\n    var bodyStyle = document.body.style;\n\n    // Sniff prefixes\n    var vendorPrefix = ('WebkitTransform' in bodyStyle) ? 'Webkit' :\n                       ('MozTransform' in bodyStyle) ? 'Moz' :\n                       ('msTransform' in bodyStyle) ? 'ms' :\n                       ('OTransform' in bodyStyle) ? 'O' : '';\n\n    if (vendorPrefix + 'Perspective' in bodyStyle) {\n      // Modern browsers with 3D support, e.g. Webkit, IE10\n      return 'translate3d';\n    } else if (vendorPrefix + 'Transform' in bodyStyle) {\n      // Browsers without 3D support, e.g. IE9\n      return 'translate';\n    } else {\n      // Browsers without translate() support, e.g. IE7-8\n      return 'margin';\n    }\n  };\n\n  /**\n   * Helpers\n   */\n\n  function clamp(n, min, max) {\n    if (n < min) return min;\n    if (n > max) return max;\n    return n;\n  }\n\n  /**\n   * (Internal) converts a percentage (`0..1`) to a bar translateX\n   * percentage (`-100%..0%`).\n   */\n\n  function toBarPerc(n) {\n    return (-1 + n) * 100;\n  }\n\n\n  /**\n   * (Internal) returns the correct CSS for changing the bar's\n   * position given an n percentage, and speed and ease from Settings\n   */\n\n  function barPositionCSS(n, speed, ease) {\n    var barCSS;\n\n    if (Settings.positionUsing === 'translate3d') {\n      barCSS = { transform: 'translate3d('+toBarPerc(n)+'%,0,0)' };\n    } else if (Settings.positionUsing === 'translate') {\n      barCSS = { transform: 'translate('+toBarPerc(n)+'%,0)' };\n    } else {\n      barCSS = { 'margin-left': toBarPerc(n)+'%' };\n    }\n\n    barCSS.transition = 'all '+speed+'ms '+ease;\n\n    return barCSS;\n  }\n\n  /**\n   * (Internal) Queues a function to be executed.\n   */\n\n  var queue = (function() {\n    var pending = [];\n    \n    function next() {\n      var fn = pending.shift();\n      if (fn) {\n        fn(next);\n      }\n    }\n\n    return function(fn) {\n      pending.push(fn);\n      if (pending.length == 1) next();\n    };\n  })();\n\n  /**\n   * (Internal) Applies css properties to an element, similar to the jQuery \n   * css method.\n   *\n   * While this helper does assist with vendor prefixed property names, it \n   * does not perform any manipulation of values prior to setting styles.\n   */\n\n  var css = (function() {\n    var cssPrefixes = [ 'Webkit', 'O', 'Moz', 'ms' ],\n        cssProps    = {};\n\n    function camelCase(string) {\n      return string.replace(/^-ms-/, 'ms-').replace(/-([\\da-z])/gi, function(match, letter) {\n        return letter.toUpperCase();\n      });\n    }\n\n    function getVendorProp(name) {\n      var style = document.body.style;\n      if (name in style) return name;\n\n      var i = cssPrefixes.length,\n          capName = name.charAt(0).toUpperCase() + name.slice(1),\n          vendorName;\n      while (i--) {\n        vendorName = cssPrefixes[i] + capName;\n        if (vendorName in style) return vendorName;\n      }\n\n      return name;\n    }\n\n    function getStyleProp(name) {\n      name = camelCase(name);\n      return cssProps[name] || (cssProps[name] = getVendorProp(name));\n    }\n\n    function applyCss(element, prop, value) {\n      prop = getStyleProp(prop);\n      element.style[prop] = value;\n    }\n\n    return function(element, properties) {\n      var args = arguments,\n          prop, \n          value;\n\n      if (args.length == 2) {\n        for (prop in properties) {\n          value = properties[prop];\n          if (value !== undefined && properties.hasOwnProperty(prop)) applyCss(element, prop, value);\n        }\n      } else {\n        applyCss(element, args[1], args[2]);\n      }\n    }\n  })();\n\n  /**\n   * (Internal) Determines if an element or space separated list of class names contains a class name.\n   */\n\n  function hasClass(element, name) {\n    var list = typeof element == 'string' ? element : classList(element);\n    return list.indexOf(' ' + name + ' ') >= 0;\n  }\n\n  /**\n   * (Internal) Adds a class to an element.\n   */\n\n  function addClass(element, name) {\n    var oldList = classList(element),\n        newList = oldList + name;\n\n    if (hasClass(oldList, name)) return; \n\n    // Trim the opening space.\n    element.className = newList.substring(1);\n  }\n\n  /**\n   * (Internal) Removes a class from an element.\n   */\n\n  function removeClass(element, name) {\n    var oldList = classList(element),\n        newList;\n\n    if (!hasClass(element, name)) return;\n\n    // Replace the class name.\n    newList = oldList.replace(' ' + name + ' ', ' ');\n\n    // Trim the opening and closing spaces.\n    element.className = newList.substring(1, newList.length - 1);\n  }\n\n  /**\n   * (Internal) Gets a space separated list of the class names on the element. \n   * The list is wrapped with a single space on each end to facilitate finding \n   * matches within the list.\n   */\n\n  function classList(element) {\n    return (' ' + (element.className || '') + ' ').replace(/\\s+/gi, ' ');\n  }\n\n  /**\n   * (Internal) Removes an element from the DOM.\n   */\n\n  function removeElement(element) {\n    element && element.parentNode && element.parentNode.removeChild(element);\n  }\n\n  return NProgress;\n});\n\n"], "mappings": ";;;;;AAAA;AAAA;AAGC,KAAC,SAAS,MAAM,SAAS;AAExB,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC9C,eAAO,OAAO;AAAA,MAChB,WAAW,OAAO,YAAY,UAAU;AACtC,eAAO,UAAU,QAAQ;AAAA,MAC3B,OAAO;AACL,aAAK,YAAY,QAAQ;AAAA,MAC3B;AAAA,IAEF,GAAG,SAAM,WAAW;AAClB,UAAI,YAAY,CAAC;AAEjB,gBAAU,UAAU;AAEpB,UAAI,WAAW,UAAU,WAAW;AAAA,QAClC,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,eAAe;AAAA,QACf,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,cAAc;AAAA,QACd,aAAa;AAAA,QACb,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AASA,gBAAU,YAAY,SAAS,SAAS;AACtC,YAAI,KAAK;AACT,aAAK,OAAO,SAAS;AACnB,kBAAQ,QAAQ,GAAG;AACnB,cAAI,UAAU,UAAa,QAAQ,eAAe,GAAG;AAAG,qBAAS,GAAG,IAAI;AAAA,QAC1E;AAEA,eAAO;AAAA,MACT;AAMA,gBAAU,SAAS;AASnB,gBAAU,MAAM,SAAS,GAAG;AAC1B,YAAI,UAAU,UAAU,UAAU;AAElC,YAAI,MAAM,GAAG,SAAS,SAAS,CAAC;AAChC,kBAAU,SAAU,MAAM,IAAI,OAAO;AAErC,YAAI,WAAW,UAAU,OAAO,CAAC,OAAO,GACpC,MAAW,SAAS,cAAc,SAAS,WAAW,GACtD,QAAW,SAAS,OACpB,OAAW,SAAS;AAExB,iBAAS;AAET,cAAM,SAAS,MAAM;AAEnB,cAAI,SAAS,kBAAkB;AAAI,qBAAS,gBAAgB,UAAU,kBAAkB;AAGxF,cAAI,KAAK,eAAe,GAAG,OAAO,IAAI,CAAC;AAEvC,cAAI,MAAM,GAAG;AAEX,gBAAI,UAAU;AAAA,cACZ,YAAY;AAAA,cACZ,SAAS;AAAA,YACX,CAAC;AACD,qBAAS;AAET,uBAAW,WAAW;AACpB,kBAAI,UAAU;AAAA,gBACZ,YAAY,SAAS,QAAQ;AAAA,gBAC7B,SAAS;AAAA,cACX,CAAC;AACD,yBAAW,WAAW;AACpB,0BAAU,OAAO;AACjB,qBAAK;AAAA,cACP,GAAG,KAAK;AAAA,YACV,GAAG,KAAK;AAAA,UACV,OAAO;AACL,uBAAW,MAAM,KAAK;AAAA,UACxB;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAEA,gBAAU,YAAY,WAAW;AAC/B,eAAO,OAAO,UAAU,WAAW;AAAA,MACrC;AASA,gBAAU,QAAQ,WAAW;AAC3B,YAAI,CAAC,UAAU;AAAQ,oBAAU,IAAI,CAAC;AAEtC,YAAI,OAAO,WAAW;AACpB,qBAAW,WAAW;AACpB,gBAAI,CAAC,UAAU;AAAQ;AACvB,sBAAU,QAAQ;AAClB,iBAAK;AAAA,UACP,GAAG,SAAS,YAAY;AAAA,QAC1B;AAEA,YAAI,SAAS;AAAS,eAAK;AAE3B,eAAO;AAAA,MACT;AAcA,gBAAU,OAAO,SAAS,OAAO;AAC/B,YAAI,CAAC,SAAS,CAAC,UAAU;AAAQ,iBAAO;AAExC,eAAO,UAAU,IAAI,MAAM,MAAM,KAAK,OAAO,CAAC,EAAE,IAAI,CAAC;AAAA,MACvD;AAMA,gBAAU,MAAM,SAAS,QAAQ;AAC/B,YAAI,IAAI,UAAU;AAElB,YAAI,CAAC,GAAG;AACN,iBAAO,UAAU,MAAM;AAAA,QACzB,OAAO;AACL,cAAI,OAAO,WAAW,UAAU;AAC9B,sBAAU,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG,KAAK,IAAI;AAAA,UACvD;AAEA,cAAI,MAAM,IAAI,QAAQ,GAAG,KAAK;AAC9B,iBAAO,UAAU,IAAI,CAAC;AAAA,QACxB;AAAA,MACF;AAEA,gBAAU,UAAU,WAAW;AAC7B,eAAO,UAAU,IAAI,KAAK,OAAO,IAAI,SAAS,WAAW;AAAA,MAC3D;AAQA,OAAC,WAAW;AACV,YAAI,UAAU,GAAG,UAAU;AAE3B,kBAAU,UAAU,SAAS,UAAU;AACrC,cAAI,CAAC,YAAY,SAAS,MAAM,MAAM,YAAY;AAChD,mBAAO;AAAA,UACT;AAEA,cAAI,YAAY,GAAG;AACjB,sBAAU,MAAM;AAAA,UAClB;AAEA;AACA;AAEA,mBAAS,OAAO,WAAW;AACzB;AACA,gBAAI,YAAY,GAAG;AACf,wBAAU;AACV,wBAAU,KAAK;AAAA,YACnB,OAAO;AACH,wBAAU,KAAK,UAAU,WAAW,OAAO;AAAA,YAC/C;AAAA,UACF,CAAC;AAED,iBAAO;AAAA,QACT;AAAA,MAEF,GAAG;AAOH,gBAAU,SAAS,SAAS,WAAW;AACrC,YAAI,UAAU,WAAW;AAAG,iBAAO,SAAS,eAAe,WAAW;AAEtE,iBAAS,SAAS,iBAAiB,gBAAgB;AAEnD,YAAI,WAAW,SAAS,cAAc,KAAK;AAC3C,iBAAS,KAAK;AACd,iBAAS,YAAY,SAAS;AAE9B,YAAI,MAAW,SAAS,cAAc,SAAS,WAAW,GACtD,OAAW,YAAY,SAAS,UAAU,UAAU,UAAU,CAAC,GAC/D,SAAW,SAAS,cAAc,SAAS,MAAM,GACjD;AAEJ,YAAI,KAAK;AAAA,UACP,YAAY;AAAA,UACZ,WAAW,iBAAiB,OAAO;AAAA,QACrC,CAAC;AAED,YAAI,CAAC,SAAS,aAAa;AACzB,oBAAU,SAAS,cAAc,SAAS,eAAe;AACzD,qBAAW,cAAc,OAAO;AAAA,QAClC;AAEA,YAAI,UAAU,SAAS,MAAM;AAC3B,mBAAS,QAAQ,yBAAyB;AAAA,QAC5C;AAEA,eAAO,YAAY,QAAQ;AAC3B,eAAO;AAAA,MACT;AAMA,gBAAU,SAAS,WAAW;AAC5B,oBAAY,SAAS,iBAAiB,gBAAgB;AACtD,oBAAY,SAAS,cAAc,SAAS,MAAM,GAAG,yBAAyB;AAC9E,YAAI,WAAW,SAAS,eAAe,WAAW;AAClD,oBAAY,cAAc,QAAQ;AAAA,MACpC;AAMA,gBAAU,aAAa,WAAW;AAChC,eAAO,CAAC,CAAC,SAAS,eAAe,WAAW;AAAA,MAC9C;AAMA,gBAAU,oBAAoB,WAAW;AAEvC,YAAI,YAAY,SAAS,KAAK;AAG9B,YAAI,eAAgB,qBAAqB,YAAa,WAClC,kBAAkB,YAAa,QAC/B,iBAAiB,YAAa,OAC9B,gBAAgB,YAAa,MAAM;AAEvD,YAAI,eAAe,iBAAiB,WAAW;AAE7C,iBAAO;AAAA,QACT,WAAW,eAAe,eAAe,WAAW;AAElD,iBAAO;AAAA,QACT,OAAO;AAEL,iBAAO;AAAA,QACT;AAAA,MACF;AAMA,eAAS,MAAM,GAAG,KAAK,KAAK;AAC1B,YAAI,IAAI;AAAK,iBAAO;AACpB,YAAI,IAAI;AAAK,iBAAO;AACpB,eAAO;AAAA,MACT;AAOA,eAAS,UAAU,GAAG;AACpB,gBAAQ,KAAK,KAAK;AAAA,MACpB;AAQA,eAAS,eAAe,GAAG,OAAO,MAAM;AACtC,YAAI;AAEJ,YAAI,SAAS,kBAAkB,eAAe;AAC5C,mBAAS,EAAE,WAAW,iBAAe,UAAU,CAAC,IAAE,SAAS;AAAA,QAC7D,WAAW,SAAS,kBAAkB,aAAa;AACjD,mBAAS,EAAE,WAAW,eAAa,UAAU,CAAC,IAAE,OAAO;AAAA,QACzD,OAAO;AACL,mBAAS,EAAE,eAAe,UAAU,CAAC,IAAE,IAAI;AAAA,QAC7C;AAEA,eAAO,aAAa,SAAO,QAAM,QAAM;AAEvC,eAAO;AAAA,MACT;AAMA,UAAI,QAAS,WAAW;AACtB,YAAI,UAAU,CAAC;AAEf,iBAAS,OAAO;AACd,cAAI,KAAK,QAAQ,MAAM;AACvB,cAAI,IAAI;AACN,eAAG,IAAI;AAAA,UACT;AAAA,QACF;AAEA,eAAO,SAAS,IAAI;AAClB,kBAAQ,KAAK,EAAE;AACf,cAAI,QAAQ,UAAU;AAAG,iBAAK;AAAA,QAChC;AAAA,MACF,EAAG;AAUH,UAAI,MAAO,WAAW;AACpB,YAAI,cAAc,CAAE,UAAU,KAAK,OAAO,IAAK,GAC3C,WAAc,CAAC;AAEnB,iBAAS,UAAU,QAAQ;AACzB,iBAAO,OAAO,QAAQ,SAAS,KAAK,EAAE,QAAQ,gBAAgB,SAAS,OAAO,QAAQ;AACpF,mBAAO,OAAO,YAAY;AAAA,UAC5B,CAAC;AAAA,QACH;AAEA,iBAAS,cAAc,MAAM;AAC3B,cAAI,QAAQ,SAAS,KAAK;AAC1B,cAAI,QAAQ;AAAO,mBAAO;AAE1B,cAAI,IAAI,YAAY,QAChB,UAAU,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC,GACrD;AACJ,iBAAO,KAAK;AACV,yBAAa,YAAY,CAAC,IAAI;AAC9B,gBAAI,cAAc;AAAO,qBAAO;AAAA,UAClC;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,aAAa,MAAM;AAC1B,iBAAO,UAAU,IAAI;AACrB,iBAAO,SAAS,IAAI,MAAM,SAAS,IAAI,IAAI,cAAc,IAAI;AAAA,QAC/D;AAEA,iBAAS,SAAS,SAAS,MAAM,OAAO;AACtC,iBAAO,aAAa,IAAI;AACxB,kBAAQ,MAAM,IAAI,IAAI;AAAA,QACxB;AAEA,eAAO,SAAS,SAAS,YAAY;AACnC,cAAI,OAAO,WACP,MACA;AAEJ,cAAI,KAAK,UAAU,GAAG;AACpB,iBAAK,QAAQ,YAAY;AACvB,sBAAQ,WAAW,IAAI;AACvB,kBAAI,UAAU,UAAa,WAAW,eAAe,IAAI;AAAG,yBAAS,SAAS,MAAM,KAAK;AAAA,YAC3F;AAAA,UACF,OAAO;AACL,qBAAS,SAAS,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,UACpC;AAAA,QACF;AAAA,MACF,EAAG;AAMH,eAAS,SAAS,SAAS,MAAM;AAC/B,YAAI,OAAO,OAAO,WAAW,WAAW,UAAU,UAAU,OAAO;AACnE,eAAO,KAAK,QAAQ,MAAM,OAAO,GAAG,KAAK;AAAA,MAC3C;AAMA,eAAS,SAAS,SAAS,MAAM;AAC/B,YAAI,UAAU,UAAU,OAAO,GAC3B,UAAU,UAAU;AAExB,YAAI,SAAS,SAAS,IAAI;AAAG;AAG7B,gBAAQ,YAAY,QAAQ,UAAU,CAAC;AAAA,MACzC;AAMA,eAAS,YAAY,SAAS,MAAM;AAClC,YAAI,UAAU,UAAU,OAAO,GAC3B;AAEJ,YAAI,CAAC,SAAS,SAAS,IAAI;AAAG;AAG9B,kBAAU,QAAQ,QAAQ,MAAM,OAAO,KAAK,GAAG;AAG/C,gBAAQ,YAAY,QAAQ,UAAU,GAAG,QAAQ,SAAS,CAAC;AAAA,MAC7D;AAQA,eAAS,UAAU,SAAS;AAC1B,gBAAQ,OAAO,QAAQ,aAAa,MAAM,KAAK,QAAQ,SAAS,GAAG;AAAA,MACrE;AAMA,eAAS,cAAc,SAAS;AAC9B,mBAAW,QAAQ,cAAc,QAAQ,WAAW,YAAY,OAAO;AAAA,MACzE;AAEA,aAAO;AAAA,IACT,CAAC;AAAA;AAAA;", "names": []}
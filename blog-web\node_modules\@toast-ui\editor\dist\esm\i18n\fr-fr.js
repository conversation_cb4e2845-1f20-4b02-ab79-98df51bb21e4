/**
 * @toast-ui/editor : i18n
 * @version 3.2.2 | Fri Feb 17 2023
 * <AUTHOR> Cloud FE Development Lab <<EMAIL>>
 * @license MIT
 */

import Editor from '@toast-ui/editor';

/**
 * @fileoverview I18N for French
 * <AUTHOR> <<EMAIL>>
 */
Editor.setLanguage(['fr', 'fr-FR'], {
    Markdown: 'Markdown',
    WYSIWYG: 'WYSIWYG',
    Write: 'Écrire',
    Preview: 'Aperçu',
    Headings: 'En-têtes',
    Paragraph: 'Paragraphe',
    Bold: 'Gras',
    Italic: 'Italique',
    Strike: 'Barré',
    Code: 'Code en ligne',
    Line: 'Ligne',
    Blockquote: 'Citation',
    'Unordered list': 'Liste non-ordonnée',
    'Ordered list': 'Liste ordonnée',
    Task: 'Tâche',
    Indent: 'Retrait',
    Outdent: 'Sortir',
    'Insert link': 'Insérer un lien',
    'Insert CodeBlock': 'Insérer un bloc de code',
    'Insert table': 'Insérer un tableau',
    'Insert image': 'Insérer une image',
    Heading: 'En-tête',
    'Image URL': "URL de l'image",
    'Select image file': 'Sélectionnez un fichier image',
    'Choose a file': 'Choisissez un fichier',
    'No file': 'Pas de fichier',
    Description: 'Description',
    OK: 'OK',
    More: 'de plus',
    Cancel: 'Annuler',
    File: 'Fichier',
    URL: 'URL',
    'Link text': 'Texte du lien',
    'Add row to up': 'Ajouter une ligne vers le haut',
    'Add row to down': 'Ajouter une ligne vers le bas',
    'Add column to left': 'Ajouter une colonne à gauche',
    'Add column to right': 'Ajouter une colonne à droite',
    'Remove row': 'Supprimer une ligne',
    'Remove column': 'Supprimer une colonne',
    'Align column to left': 'Aligner à gauche',
    'Align column to center': 'Aligner au centre',
    'Align column to right': 'Aligner à droite',
    'Remove table': 'Supprimer le tableau',
    'Would you like to paste as table?': 'Voulez-vous coller ce contenu en tant que tableau ?',
    'Text color': 'Couleur du texte',
    'Auto scroll enabled': 'Défilement automatique activé',
    'Auto scroll disabled': 'Défilement automatique désactivé',
    'Choose language': 'Choix de la langue',
});

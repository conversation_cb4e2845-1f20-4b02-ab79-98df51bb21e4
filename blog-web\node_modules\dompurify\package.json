{"scripts": {"lint": "xo src/*.js", "format": "npm run format:js && npm run format:md", "format:md": "prettier --write --parser markdown '**/*.md'", "format:js": "prettier --write '{src,demos,scripts,test,website}/*.js'", "commit-amend-build": "scripts/commit-amend-build.sh", "prebuild": "rimraf dist/**", "dev": "cross-env NODE_ENV=development BABEL_ENV=rollup rollup -w -c -o dist/purify.js", "build": "cross-env run-p build:umd build:umd:min build:es build:cjs", "build:umd": "cross-env NODE_ENV=development BABEL_ENV=rollup rollup -c -f umd -o dist/purify.js", "build:umd:min": "cross-env NODE_ENV=production BABEL_ENV=rollup rollup -c -f umd -o dist/purify.min.js", "build:es": "cross-env NODE_ENV=development BABEL_ENV=rollup rollup -c -f es -o dist/purify.es.js", "build:cjs": "cross-env NODE_ENV=development BABEL_ENV=rollup rollup -c -f cjs -o dist/purify.cjs.js", "test:jsdom": "cross-env NODE_ENV=test BABEL_ENV=rollup node test/jsdom-node-runner --dot", "test:karma": "cross-env NODE_ENV=test BABEL_ENV=rollup karma start test/karma.conf.js --log-level warn ", "test:ci": "cross-env NODE_ENV=test BABEL_ENV=rollup npm run test:jsdom && npm run test:karma -- --log-level error --reporters dots --single-run --shouldTestOnBrowserStack=\"${TEST_BROWSERSTACK}\" --shouldProbeOnly=\"${TEST_PROBE_ONLY}\"", "test": "cross-env NODE_ENV=test BABEL_ENV=rollup npm run lint &&  npm run test:jsdom && npm run test:karma -- --browsers Chrome"}, "main": "dist/purify.cjs.js", "module": "dist/purify.es.js", "browser": "dist/purify.js", "files": ["dist"], "pre-commit": ["lint", "build", "commit-amend-build"], "xo": {"semicolon": true, "space": 2, "extends": ["prettier"], "plugins": ["prettier"], "rules": {"import/no-useless-path-segments": 0, "unicorn/prefer-optional-catch-binding": 0, "unicorn/prefer-node-remove": 0, "prettier/prettier": ["error", {"trailingComma": "es5", "singleQuote": true}], "camelcase": ["error", {"properties": "never"}]}, "globals": ["window", "VERSION"]}, "devDependencies": {"@babel/core": "^7.17.8", "@babel/preset-env": "^7.16.11", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^21.0.3", "@rollup/plugin-node-resolve": "^13.1.3", "@rollup/plugin-replace": "^4.0.0", "@types/dompurify": "^2.3.3", "@types/estree": "^1.0.0", "cross-env": "^7.0.3", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jquery": "^3.6.0", "jsdom": "^20.0.0", "karma": "^6.3.17", "karma-browserstack-launcher": "^1.5.1", "karma-chrome-launcher": "^3.1.0", "karma-firefox-launcher": "^2.1.2", "karma-qunit": "^4.1.2", "karma-rollup-preprocessor": "^7.0.8", "lodash.sample": "^4.2.1", "minimist": "^1.2.6", "npm-run-all": "^4.1.5", "pre-commit": "^1.2.2", "prettier": "^2.5.1", "qunit": "^2.4.1", "qunit-tap": "^1.5.0", "rimraf": "^3.0.2", "rollup": "^2.70.1", "rollup-plugin-includepaths": "^0.2.3", "rollup-plugin-terser": "^7.0.2", "xo": "^0.48.0"}, "resolutions": {"natives": "1.1.6"}, "name": "dompurify", "description": "DOMPurify is a DOM-only, super-fast, uber-tolerant XSS sanitizer for HTML, MathML and SVG. It's written in JavaScript and works in all modern browsers (Safari, Opera (15+), Internet Explorer (10+), Firefox and Chrome - as well as almost anything else using Blink or WebKit). DOMPurify is written by security people who have vast background in web attacks and XSS. Fear not.", "version": "2.5.8", "directories": {"test": "test"}, "repository": {"type": "git", "url": "git://github.com/cure53/DOMPurify.git"}, "keywords": ["dom", "xss", "html", "svg", "mathml", "security", "secure", "sanitizer", "sanitize", "filter", "purify"], "author": "<PERSON> <<EMAIL>> (https://cure53.de/)", "license": "(MPL-2.0 OR Apache-2.0)", "bugs": {"url": "https://github.com/cure53/DOMPurify/issues"}, "homepage": "https://github.com/cure53/DOMPurify"}